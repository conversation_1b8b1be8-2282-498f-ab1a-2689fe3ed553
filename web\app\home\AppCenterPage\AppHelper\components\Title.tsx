import titleBg from '@/assets/images/search/titleBg.png'
import { useSearchParams } from 'next/navigation'

export default function AppHelperTitle() {
  const searchParams = useSearchParams()

  // 获取URL参数
  // const appId = searchParams.get('appId')
  const appName = searchParams.get('appName')
  const appDescription = searchParams.get('appDescription')

  return (
    <div className="mb-8 text-center font-[PingFangSC]">
      <div className="mb-1 px-[15px] pr-[27px] text-[29px] font-[600] text-[#2C2C36]" style={{ backgroundImage: `url(${titleBg.src})`, backgroundSize: '100% 100%', backgroundRepeat: 'no-repeat' }}>
        {appName || '问答助手'}
      </div>
      <div className="text-[16px] font-[400] text-[#828DA2]">
        {appDescription || ''}
      </div>
    </div>
  )
}
