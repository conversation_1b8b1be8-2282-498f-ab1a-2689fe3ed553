'use client'
import AppHelperTitle from '@/app/home/<USER>/AppHelper/components/Title'
import AppHelperSearch from '@/app/home/<USER>/AppHelper/components/Search'
import AppHelperQuestion from '@/app/home/<USER>/AppHelper/components/Question'
export default function AppHelper() {
  return (
    <>
      <div className="flex size-full flex-col items-center py-[124px]" style={{ background: 'linear-gradient( 180deg, #F3F8FF 0%, #F3F8FF 74%, #FFFFFF 100%)' }}>
        {/* 标题 */}
        <AppHelperTitle />
        {/* 输入框 */}
        <AppHelperSearch />
        {/* 推荐问题 */}
        <AppHelperQuestion />
      </div>
    </>
  )
}
